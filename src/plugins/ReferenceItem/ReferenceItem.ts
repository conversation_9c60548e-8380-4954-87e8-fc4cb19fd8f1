/**
 * 智能参考文献段落扩展
 * 自动检测参考文献格式并应用相应样式
 */
import Paragraph from '@tiptap/extension-paragraph'

// 参考文献检测函数
const isReferenceItem = (text: string): boolean => {
  if (!text || typeof text !== 'string') return false
  const trimmedText = text.trim()

  // 检测参考文献条目格式：[数字] 开头
  return /^\[\d+\]/.test(trimmedText)
}

// 参考文献标识检测函数
const isReferenceIdentifier = (text: string): boolean => {
  if (!text || typeof text !== 'string') return false
  const trimmedText = text.trim()

  // 检查是否是参考文献标识段落
  const referenceIdentifiers = [
    /^参考文献\s*$/i,
    /^references\s*$/i,
    /^bibliography\s*$/i,
    /^文献\s*$/i,
    /^reference\s*$/i
  ]

  return referenceIdentifiers.some((pattern) => pattern.test(trimmedText))
}

/**
 * 智能参考文献段落扩展
 * 替换默认的 paragraph，提供参考文献自动检测和样式应用功能
 */
export const RefParagraph = Paragraph.extend({
  name: 'paragraph', // 保持默认名称以确保兼容性

  addAttributes() {
    return {
      ...this.parent?.(),
      // 参考文献标识属性
      ref: {
        default: false,
        parseHTML: (element) => {
          const text = element.textContent || ''
          return isReferenceItem(text) || element.getAttribute('data-ref') === 'true'
        },
        renderHTML: (attributes) => {
          if (attributes.ref) {
            return {
              'data-ref': 'true'
            }
          }
          return {}
        }
      },
      // 参考文献标识符属性
      isReferenceHeading: {
        default: false,
        parseHTML: (element) => {
          const text = element.textContent || ''
          return (
            isReferenceIdentifier(text) || element.getAttribute('data-reference-heading') === 'true'
          )
        },
        renderHTML: (attributes) => {
          if (attributes.isReferenceHeading) {
            return {
              'data-reference-heading': 'true'
            }
          }
          return {}
        }
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'p',
        getAttrs: (element) => {
          const text = element.textContent || ''
          const isRef = isReferenceItem(text) || element.getAttribute('data-ref') === 'true'
          const isHeading =
            isReferenceIdentifier(text) || element.getAttribute('data-reference-heading') === 'true'

          return {
            ref: isRef,
            isReferenceHeading: isHeading
          }
        }
      }
    ]
  },

  renderHTML({ node, HTMLAttributes }) {
    const text = node.textContent || ''
    const isRef = node.attrs.ref || isReferenceItem(text)
    const isHeading = node.attrs.isReferenceHeading || isReferenceIdentifier(text)

    // 构建class名称
    let className = 'my-custom-paragraph'

    if (isRef) {
      className += ' ref-para no-indent reference-item'
    }

    if (isHeading) {
      className += ' reference-heading'
    }

    // 合并属性
    const mergedAttributes = {
      ...HTMLAttributes,
      class: className,
      ...(isRef && { 'data-ref': 'true' }),
      ...(isHeading && { 'data-reference-heading': 'true' })
    }

    return ['p', mergedAttributes, 0]
  },

  addCommands() {
    return {
      ...this.parent?.(),
      // 设置参考文献属性
      setReferenceItem:
        () =>
        ({ commands }: any) => {
          return commands.updateAttributes(this.name, { ref: true })
        },
      // 取消参考文献属性
      unsetReferenceItem:
        () =>
        ({ commands }: any) => {
          return commands.updateAttributes(this.name, { ref: false })
        },
      // 设置参考文献标题属性
      setReferenceHeading:
        () =>
        ({ commands }: any) => {
          return commands.updateAttributes(this.name, { isReferenceHeading: true })
        },
      // 取消参考文献标题属性
      unsetReferenceHeading:
        () =>
        ({ commands }: any) => {
          return commands.updateAttributes(this.name, { isReferenceHeading: false })
        }
    }
  },

  // 添加键盘快捷键支持
  addKeyboardShortcuts() {
    return {
      // Ctrl+Shift+R 切换参考文献状态
      'Mod-Shift-r': () => {
        const { state } = this.editor
        const { selection } = state
        const node = state.doc.nodeAt(selection.from)

        if (node && node.type.name === this.name) {
          const isCurrentlyReference = node.attrs.ref
          return this.editor.commands.updateAttributes(this.name, {
            ref: !isCurrentlyReference
          })
        }

        return false
      }
    }
  }
})

/**
 * 保持向后兼容的 ReferenceItem 扩展
 * @deprecated 建议使用 RefParagraph 替代
 */
export const ReferenceItem = Paragraph.extend({
  name: 'ReferenceItem',
  addCommands() {
    return {
      setImage:
        (options: any) =>
        ({ commands }: any) => {
          return commands.insertContent({
            type: this.name,
            attrs: options
          })
        }
    }
  },
  addAttributes() {
    return {
      class: {
        default: 'no-indent'
      }
    }
  }
})
